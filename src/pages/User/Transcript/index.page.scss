// Variables
$primary-color: #3B60E4;
$error-color: #e74c3c;
$border-color: #f0f0f0;
$hover-color: #e0e0e0;
$text-color: #999;
$background-light: #f5f5f5;
$background-hover: #e6e6e6;
$background-active: #e6f7ff;

.transcription-layout {
  background-color: #F4F8F9;
  min-height: 100vh;
  
  // Ensure the background covers all child elements
  .container-fluid,
  .row,
  [data-panel-group] {
    background-color: #F4F8F9;
  }
  
  // Ensure the left section maintains the background
  .left-section {
    background-color: #F4F8F9;
  }
  
  // Ensure the bottom container has the background
  .bottom-container {
    background-color: #F4F8F9 !important;
  }
  
  // Ensure the right side panel/sidebar has the same background
  [data-panel] {
    background-color: #F4F8F9;
    overflow: auto;
  }
  
  // Ensure the sidebar component has the background
  .sidebar {
    background-color: #F4F8F9;
  }
  
  // Override the bg-white class on sidebar container
  .sidebar-container.bg-white {
    background-color: #F4F8F9 !important;
  }
  
  // Ensure all sidebar elements have the background
  .sidebar-header,
  .sidebar-content {
    background-color: #F4F8F9;
  }
}

// Loading Styles
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  width: 100%;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid $primary-color;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.error-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  width: 100%;
  color: $error-color;
}

// Resize Handle Styles
.resize-handle {
  width: 8px;
  background: $border-color;
  position: relative;
  cursor: col-resize;
  transition: background-color 0.2s;
  z-index: 1000;

  &:hover {
    background: $hover-color;
  }

  &-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1001;
  }

  &-dots {
    display: flex;
    flex-direction: column;
    gap: 4px;
    z-index: 1002;
  }

  .dot {
    width: 4px;
    height: 4px;
    background: $text-color;
    border-radius: 50%;
    transition: background-color 0.2s;
    z-index: 1003;
  }

  &:hover .dot {
    background: darken($text-color, 20%);
  }
}

// Panel Styles
[data-panel-group] {
  height: 100%;
}

// Tabs Styles
.ant-tabs {
  &-nav {
    margin-bottom: 0 !important;
    padding: 0 8px;
    position: relative;

    &-wrap {
      padding: 0 8px;
      overflow: hidden;
    }

    &-more {
      padding: 8px 12px !important;
      background: $background-light !important;
      border-radius: 4px !important;
      margin: 4px 0 !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;

      &:hover {
        background: $background-hover !important;
      }

      .anticon {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
      }
    }

    &-operations {
      position: relative;
      z-index: 1;
      display: flex !important;
      align-items: center !important;
    }
  }

  &-dropdown {
    min-width: 200px !important;
    z-index: 1000;

    &-menu-item {
      padding: 8px 16px !important;
      font-size: 14px !important;
    }
  }

  &-tab {
    padding: 8px 16px !important;
    margin: 0 4px !important;
    border-radius: 4px !important;
    transition: all 0.3s !important;

    &:hover {
      background: $background-light !important;
    }

    &-active {
      background: $background-active !important;
    }
  }
}

// Responsive Styles
@media (min-width: 768px) {
  .ant-tabs-tab[data-node-key="4"],
  .ant-tabs-tab[data-node-key="5"] {
    display: none !important;
  }
}

@media (max-width: 767px) {
  .ant-tabs {
    &-nav {
      padding: 0 4px !important;
    }

    &-tab {
      padding: 6px 12px !important;
      margin: 0 2px !important;
      font-size: 12px !important;
      display: flex !important;
    }

    &-nav-more {
      padding: 6px 10px !important;
      margin: 2px 0 !important;
    }

    &-dropdown-menu-item {
      padding: 6px 12px !important;
      font-size: 12px !important;
    }
  }
}

// Scrollbar Styles (matching SettingsModal)
.transcript-container {
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #798593;
    padding: 0.3rem;
    border-radius: 0.3rem;
  }
}

// Apply scrollbar styles to other scrollable containers
.bottom-container {
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #798593;
    padding: 0.3rem;
    border-radius: 0.3rem;
  }
}

// Animation
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.transcript-card {
  padding: 0.5rem;
  border-radius: 10px;
  margin-bottom: 1rem;
  transition: all 0.3s ease;

  &:hover {
    background-color: #f5f5f5;
  }

  &.active {
    background-color: #e6f7ff;
    border-color: #3B60E4;
  }

  .transcript-time {
    color: #666;
    font-size: 0.875rem;
    margin-bottom: 0.1rem;
  }

  .transcript-content {
    color: #333;
    font-size: 1rem;
    line-height: 1.5;
  }
}

.empty-state-message {
  text-align: center;
  padding: 2rem;
  color: #666;

  p {
    margin-bottom: 0.5rem;
    
    &:first-child {
      font-size: 1.1rem;
      font-weight: 500;
      color: #333;
    }

    &.text-muted {
      font-size: 0.9rem;
      color: #888;
    }
  }
}

.status-container {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background-color: #fff;
  border-left: 1px solid #e0e0e0;
}

.participant-card {
  padding: 0.6rem;
  border: 1px solid #e0e0e0;
  border-radius: 10px;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
  background-color: #fff;

  &:hover {
    background-color: #f5f5f5;
  }

  .participant-avatar {
    margin-right: 0.75rem;
  }

  .participant-info {
    h6 {
      margin-bottom: 0.25rem;
      color: #333;
      font-size: 1rem;
    }

    p {
      color: #666;
      font-size: 0.875rem;
      margin-bottom: 0;
    }
  }
}